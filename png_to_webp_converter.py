#!/usr/bin/env python3
"""
Convertisseur PNG vers WebP
Ce script permet de convertir des images PNG en format WebP avec différentes options.
"""

import os
import sys
from pathlib import Path
from PIL import Image
import argparse


def convert_png_to_webp(input_path, output_path=None, quality=80, lossless=False):
    """
    Convertit une image PNG en WebP.
    
    Args:
        input_path (str): Chemin vers le fichier PNG d'entrée
        output_path (str, optional): Chemin de sortie. Si None, remplace l'extension par .webp
        quality (int): Qualité de compression (0-100, défaut: 80)
        lossless (bool): Si True, utilise la compression sans perte
    
    Returns:
        bool: True si la conversion a réussi, False sinon
    """
    try:
        # Vérifier que le fichier d'entrée existe
        if not os.path.exists(input_path):
            print(f"Erreur: Le fichier {input_path} n'existe pas.")
            return False
        
        # Vérifier que c'est bien un fichier PNG
        if not input_path.lower().endswith('.png'):
            print(f"Erreur: {input_path} n'est pas un fichier PNG.")
            return False
        
        # Définir le chemin de sortie si non spécifié
        if output_path is None:
            output_path = os.path.splitext(input_path)[0] + '.webp'
        
        # Ouvrir et convertir l'image
        with Image.open(input_path) as img:
            # Convertir en RGB si l'image est en RGBA (pour éviter les problèmes de transparence)
            if img.mode in ('RGBA', 'LA'):
                # Créer un fond blanc pour les images avec transparence
                background = Image.new('RGB', img.size, (255, 255, 255))
                if img.mode == 'RGBA':
                    background.paste(img, mask=img.split()[-1])  # Utiliser le canal alpha comme masque
                else:
                    background.paste(img)
                img = background
            elif img.mode != 'RGB':
                img = img.convert('RGB')
            
            # Sauvegarder en WebP
            if lossless:
                img.save(output_path, 'WebP', lossless=True)
            else:
                img.save(output_path, 'WebP', quality=quality, optimize=True)
        
        print(f"✓ Converti: {input_path} → {output_path}")
        return True
        
    except Exception as e:
        print(f"Erreur lors de la conversion de {input_path}: {str(e)}")
        return False


def convert_directory(input_dir, output_dir=None, quality=80, lossless=False, recursive=False):
    """
    Convertit tous les fichiers PNG d'un répertoire en WebP.
    
    Args:
        input_dir (str): Répertoire contenant les fichiers PNG
        output_dir (str, optional): Répertoire de sortie. Si None, utilise le même répertoire
        quality (int): Qualité de compression (0-100)
        lossless (bool): Si True, utilise la compression sans perte
        recursive (bool): Si True, traite les sous-répertoires récursivement
    
    Returns:
        tuple: (nombre_convertis, nombre_total)
    """
    if not os.path.exists(input_dir):
        print(f"Erreur: Le répertoire {input_dir} n'existe pas.")
        return 0, 0
    
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    converted = 0
    total = 0
    
    # Fonction pour traiter un répertoire
    def process_directory(current_dir, current_output_dir):
        nonlocal converted, total
        
        for filename in os.listdir(current_dir):
            filepath = os.path.join(current_dir, filename)
            
            if os.path.isfile(filepath) and filename.lower().endswith('.png'):
                total += 1
                
                if current_output_dir:
                    output_path = os.path.join(current_output_dir, 
                                             os.path.splitext(filename)[0] + '.webp')
                else:
                    output_path = None
                
                if convert_png_to_webp(filepath, output_path, quality, lossless):
                    converted += 1
            
            elif os.path.isdir(filepath) and recursive:
                subdir_name = os.path.basename(filepath)
                if current_output_dir:
                    sub_output_dir = os.path.join(current_output_dir, subdir_name)
                    os.makedirs(sub_output_dir, exist_ok=True)
                else:
                    sub_output_dir = None
                
                process_directory(filepath, sub_output_dir)
    
    process_directory(input_dir, output_dir)
    return converted, total


def main():
    parser = argparse.ArgumentParser(description='Convertit des images PNG en WebP')
    parser.add_argument('input', help='Fichier PNG ou répertoire à convertir')
    parser.add_argument('-o', '--output', help='Fichier ou répertoire de sortie')
    parser.add_argument('-q', '--quality', type=int, default=80, 
                       help='Qualité de compression (0-100, défaut: 80)')
    parser.add_argument('-l', '--lossless', action='store_true',
                       help='Utiliser la compression sans perte')
    parser.add_argument('-r', '--recursive', action='store_true',
                       help='Traiter les sous-répertoires récursivement')
    
    args = parser.parse_args()
    
    # Vérifier que Pillow est installé
    try:
        from PIL import Image
    except ImportError:
        print("Erreur: La bibliothèque Pillow n'est pas installée.")
        print("Installez-la avec: pip install Pillow")
        sys.exit(1)
    
    # Vérifier la qualité
    if not 0 <= args.quality <= 100:
        print("Erreur: La qualité doit être entre 0 et 100.")
        sys.exit(1)
    
    input_path = args.input
    
    if os.path.isfile(input_path):
        # Convertir un seul fichier
        success = convert_png_to_webp(input_path, args.output, args.quality, args.lossless)
        sys.exit(0 if success else 1)
    
    elif os.path.isdir(input_path):
        # Convertir un répertoire
        converted, total = convert_directory(input_path, args.output, args.quality, 
                                           args.lossless, args.recursive)
        print(f"\nRésumé: {converted}/{total} fichiers convertis avec succès.")
        sys.exit(0 if converted == total else 1)
    
    else:
        print(f"Erreur: {input_path} n'existe pas.")
        sys.exit(1)


if __name__ == '__main__':
    main()
