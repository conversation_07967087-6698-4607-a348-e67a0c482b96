# Convertisseur PNG vers WebP

Ce script Python permet de convertir des images PNG en format WebP avec différentes options de qualité et de compression.

## Installation

1. Installez les dépendances :
```bash
pip install -r requirements.txt
```

## Utilisation

### Convertir un seul fichier
```bash
python png_to_webp_converter.py image.png
```

### Convertir avec un nom de sortie spécifique
```bash
python png_to_webp_converter.py image.png -o image_convertie.webp
```

### Convertir avec une qualité spécifique (0-100)
```bash
python png_to_webp_converter.py image.png -q 90
```

### Convertir sans perte (lossless)
```bash
python png_to_webp_converter.py image.png -l
```

### Convertir tous les PNG d'un dossier
```bash
python png_to_webp_converter.py dossier_images/
```

### Convertir récursivement (sous-dossiers inclus)
```bash
python png_to_webp_converter.py dossier_images/ -r
```

### Convertir vers un dossier de sortie différent
```bash
python png_to_webp_converter.py dossier_images/ -o dossier_webp/
```

## Options

- `-o, --output` : Fichier ou répertoire de sortie
- `-q, --quality` : Qualité de compression (0-100, défaut: 80)
- `-l, --lossless` : Utiliser la compression sans perte
- `-r, --recursive` : Traiter les sous-répertoires récursivement

## Exemples d'utilisation en tant que module

```python
from png_to_webp_converter import convert_png_to_webp, convert_directory

# Convertir un seul fichier
convert_png_to_webp('image.png', 'image.webp', quality=85)

# Convertir un dossier
converted, total = convert_directory('images/', 'webp_images/', quality=90)
print(f"{converted}/{total} fichiers convertis")
```

## Fonctionnalités

- ✅ Conversion PNG vers WebP
- ✅ Gestion de la transparence (conversion en fond blanc)
- ✅ Compression avec perte et sans perte
- ✅ Traitement par lot de dossiers
- ✅ Traitement récursif des sous-dossiers
- ✅ Gestion des erreurs
- ✅ Interface en ligne de commande
- ✅ Utilisable comme module Python
